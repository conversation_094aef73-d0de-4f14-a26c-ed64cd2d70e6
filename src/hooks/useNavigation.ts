'use client'

import { createContext, useContext } from 'react'

export type NavigationView = 'search' | 'magic' | 'personal' | 'public' | 'settings' | 'timeframe-edit'

export interface TimeframeEditingState {
  itemId: string
  mode: 'edit' | 'create'
  timeframeId?: string
  returnView: NavigationView
}

export interface NavigationContextType {
  activeView: NavigationView
  setActiveView: (view: NavigationView) => void
  timeframeEditingState: TimeframeEditingState | null
  enterTimeframeEditing: (state: TimeframeEditingState) => void
  exitTimeframeEditing: () => void
}

export const NavigationContext = createContext<NavigationContextType>({
  activeView: 'search',
  setActiveView: () => {},
  timeframeEditingState: null,
  enterTimeframeEditing: () => {},
  exitTimeframeEditing: () => {},
})

export const useNavigation = () => {
  const context = useContext(NavigationContext)
  if (!context) {
    throw new Error('useNavigation must be used within a NavigationProvider')
  }
  return context
}
