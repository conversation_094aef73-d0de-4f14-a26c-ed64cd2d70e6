'use client'

import { ReactNode, useState, useEffect, useCallback, useMemo } from 'react'
import { I18nContext } from '@/hooks/useI18n'
import { 
  SupportedLanguage, 
  TranslationValues, 
  DEFAULT_LANGUAGE, 
  SUPPORTED_LANGUAGES 
} from '@/lib/i18n/types'
import { 
  loadTranslations, 
  getTranslation, 
  getStoredLanguage, 
  storeLanguage,
  detectBrowserLanguage 
} from '@/lib/i18n/utils'

interface I18nProviderProps {
  children: ReactNode
}

export function I18nProvider({ children }: I18nProviderProps) {
  const [language, setLanguageState] = useState<SupportedLanguage>(DEFAULT_LANGUAGE)
  const [translations, setTranslations] = useState<Record<string, any>>({})
  const [fallbackTranslations, setFallbackTranslations] = useState<Record<string, any>>({})
  const [isLoading, setIsLoading] = useState(true)

  // Load translations for a specific language
  const loadLanguageTranslations = useCallback(async (lang: SupportedLanguage) => {
    try {
      setIsLoading(true)
      const newTranslations = await loadTranslations(lang)
      setTranslations(newTranslations)
      
      // Load fallback translations (English) if current language is not English
      if (lang !== DEFAULT_LANGUAGE) {
        const fallback = await loadTranslations(DEFAULT_LANGUAGE)
        setFallbackTranslations(fallback)
      } else {
        setFallbackTranslations({})
      }
    } catch (error) {
      console.error('Failed to load translations:', error)
      // On error, try to load default language
      if (lang !== DEFAULT_LANGUAGE) {
        const fallback = await loadTranslations(DEFAULT_LANGUAGE)
        setTranslations(fallback)
        setFallbackTranslations({})
      }
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Initialize language on mount
  useEffect(() => {
    const initializeLanguage = async () => {
      // Try to get stored language first
      let initialLanguage = getStoredLanguage()
      
      // If no stored language, try to detect from browser
      if (initialLanguage === DEFAULT_LANGUAGE) {
        const detectedLanguage = detectBrowserLanguage()
        if (detectedLanguage !== DEFAULT_LANGUAGE) {
          initialLanguage = detectedLanguage
          storeLanguage(initialLanguage)
        }
      }
      
      setLanguageState(initialLanguage)
      await loadLanguageTranslations(initialLanguage)
    }

    initializeLanguage()
  }, [loadLanguageTranslations])

  // Change language function
  const setLanguage = useCallback(async (newLanguage: SupportedLanguage) => {
    if (newLanguage === language) return
    
    setLanguageState(newLanguage)
    storeLanguage(newLanguage)
    await loadLanguageTranslations(newLanguage)
    
    // Update document language attribute
    if (typeof document !== 'undefined') {
      document.documentElement.lang = newLanguage
    }
  }, [language, loadLanguageTranslations])

  // Translation function
  const t = useCallback((key: string, values?: TranslationValues): string => {
    return getTranslation(translations, key, values, fallbackTranslations)
  }, [translations, fallbackTranslations])

  // Context value
  const contextValue = useMemo(() => ({
    language,
    setLanguage,
    t,
    isLoading,
    availableLanguages: SUPPORTED_LANGUAGES
  }), [language, setLanguage, t, isLoading])

  return (
    <I18nContext.Provider value={contextValue}>
      {children}
    </I18nContext.Provider>
  )
}
