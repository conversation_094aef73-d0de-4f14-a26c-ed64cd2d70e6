import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { Queue } from '@/lib/types/queue'

// Mock the hooks and services before importing components
jest.mock('@/hooks/useAuth')
jest.mock('@/hooks/useQueue')
jest.mock('@/hooks/useDraftQueue', () => ({
  useDraftQueue: () => ({
    enterEditMode: jest.fn()
  })
}))
jest.mock('@/hooks/useNavigation', () => ({
  useNavigation: () => ({
    setActiveView: jest.fn()
  })
}))
jest.mock('@/lib/services/firebase', () => ({
  firebaseService: {
    updateQueue: jest.fn(),
    getUserQueues: jest.fn(),
    duplicateQueue: jest.fn()
  }
}))

// Import components after mocking
import { PersonalQueueCard } from '@/components/personal-queues/PersonalQueueCard'
import { useAuth } from '@/hooks/useAuth'
import { useQueue } from '@/hooks/useQueue'
import { firebaseService } from '@/lib/services/firebase'

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>
const mockUseQueue = useQueue as jest.MockedFunction<typeof useQueue>
const mockFirebaseService = firebaseService as jest.Mocked<typeof firebaseService>

// Mock queue data
const mockQueue: Queue = {
  id: 'test-queue-1',
  userId: 'test-user',
  isPublic: false,
  queueData: {
    items: [],
    currentIndex: 0,
    isPlaying: false,
    queueLoopCount: -1,
    shuffle: false,
    volume: 1,
    timestamp: Date.now()
  },
  metadata: {
    title: 'Test Queue',
    videoCount: 0,
    totalDuration: 0,
    firstVideoThumbnail: '',
    createdAt: Date.now(),
    lastModified: Date.now(),
    viewCount: 0,
    isPublic: false
  },
  createdAt: Date.now(),
  lastModified: Date.now()
}

describe('Personal Queue Privacy Toggle Optimization', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks()
    
    // Setup default mock implementations
    mockUseAuth.mockReturnValue({
      user: { uid: 'test-user', email: '<EMAIL>' },
      isAuthenticated: true,
      signIn: jest.fn(),
      signOut: jest.fn(),
      loading: false
    })
    
    mockUseQueue.mockReturnValue({
      items: [],
      currentIndex: 0,
      isPlaying: false,
      queueLoopCount: -1,
      shuffle: false,
      volume: 1,
      timestamp: Date.now(),
      addVideo: jest.fn(),
      removeVideo: jest.fn(),
      moveVideo: jest.fn(),
      clearQueue: jest.fn(),
      loadQueue: jest.fn(),
      playVideo: jest.fn(),
      pauseVideo: jest.fn(),
      nextVideo: jest.fn(),
      previousVideo: jest.fn(),
      seekTo: jest.fn(),
      setVolume: jest.fn(),
      setCurrentIndex: jest.fn(),
      setPlaying: jest.fn(),
      setQueueLoopCount: jest.fn(),
      setShuffle: jest.fn(),
      restartQueue: jest.fn(),
      currentVideo: null,
      hasNext: false,
      hasPrevious: false,
      queueDuration: 0,
      saveQueue: jest.fn(),
      shareCurrentQueue: jest.fn()
    })
    
    // Mock Firebase service methods
    mockFirebaseService.getUserQueues.mockResolvedValue([mockQueue])
    mockFirebaseService.updateQueue.mockResolvedValue(true)
  })

  test('should update only the specific queue when privacy is toggled, not refresh entire list', async () => {
    const mockOnUpdate = jest.fn()
    const mockOnPrivacyUpdate = jest.fn()

    render(
      <PersonalQueueCard
        queue={mockQueue}
        onLoad={jest.fn()}
        onDelete={jest.fn()}
        onPrivacyUpdate={mockOnPrivacyUpdate}
        onUpdate={mockOnUpdate}
      />
    )

    // Find and click the privacy toggle checkbox
    const privacyToggle = screen.getByRole('checkbox')
    fireEvent.click(privacyToggle)

    // Wait for the async operation to complete
    await waitFor(() => {
      expect(mockFirebaseService.updateQueue).toHaveBeenCalledWith(
        'test-queue-1',
        { isPublic: true },
        'test-user'
      )
    })

    // Verify that onUpdate was called with the updated queue (optimized approach)
    expect(mockOnUpdate).toHaveBeenCalledWith({
      ...mockQueue,
      isPublic: true,
      lastModified: expect.any(Number)
    })

    // Verify that onPrivacyUpdate was NOT called (this would trigger a full refresh)
    expect(mockOnPrivacyUpdate).not.toHaveBeenCalled()
  })

  test('should sync local isPublic state when queue prop changes', async () => {
    const { rerender } = render(
      <PersonalQueueCard
        queue={mockQueue}
        onLoad={jest.fn()}
        onDelete={jest.fn()}
        onUpdate={jest.fn()}
      />
    )

    // Initially should show "Make Public" since queue is private
    expect(screen.getByText(/make public/i)).toBeInTheDocument()
    // Privacy badge should show "Private"
    expect(screen.getByText('Private')).toBeInTheDocument()

    // Update the queue prop to be public
    const updatedQueue = { ...mockQueue, isPublic: true }
    rerender(
      <PersonalQueueCard
        queue={updatedQueue}
        onLoad={jest.fn()}
        onDelete={jest.fn()}
        onUpdate={jest.fn()}
      />
    )

    // Should now show "Make Private" since queue is public
    await waitFor(() => {
      expect(screen.getByText(/make private/i)).toBeInTheDocument()
      expect(screen.getByText('Public')).toBeInTheDocument()
    })
  })

  test('should prevent multiple simultaneous privacy updates', async () => {
    const mockOnUpdate = jest.fn()

    // Make updateQueue take some time to simulate network delay
    mockFirebaseService.updateQueue.mockImplementation(() =>
      new Promise(resolve => setTimeout(() => resolve(true), 100))
    )

    render(
      <PersonalQueueCard
        queue={mockQueue}
        onLoad={jest.fn()}
        onDelete={jest.fn()}
        onUpdate={mockOnUpdate}
      />
    )

    const privacyToggle = screen.getByRole('checkbox')

    // Click multiple times rapidly
    fireEvent.click(privacyToggle)
    fireEvent.click(privacyToggle)
    fireEvent.click(privacyToggle)

    // Wait for operations to complete
    await waitFor(() => {
      expect(mockFirebaseService.updateQueue).toHaveBeenCalledTimes(1)
    }, { timeout: 200 })

    // Wait a bit more to ensure the first operation completes
    await waitFor(() => {
      expect(mockOnUpdate).toHaveBeenCalledTimes(1)
    }, { timeout: 200 })
  })
})
