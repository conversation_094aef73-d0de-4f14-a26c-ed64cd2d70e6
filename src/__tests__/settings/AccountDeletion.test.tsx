import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { AccountDeletionSection } from '@/components/settings/AccountDeletionSection'
import { useAuth } from '@/hooks/useAuth'
import { useNavigation } from '@/hooks/useNavigation'
import { useToast } from '@/components/providers/ToastProvider'
import { firebaseAccountService } from '@/lib/services/firebase-account'

// Mock the hooks and services
jest.mock('@/hooks/useAuth')
jest.mock('@/hooks/useNavigation')
jest.mock('@/components/providers/ToastProvider')
jest.mock('@/lib/services/firebase-account')

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>
const mockUseNavigation = useNavigation as jest.MockedFunction<typeof useNavigation>
const mockUseToast = useToast as jest.MockedFunction<typeof useToast>
const mockFirebaseAccountService = firebaseAccountService as jest.Mocked<typeof firebaseAccountService>

describe('AccountDeletionSection', () => {
  const mockDeleteAccount = jest.fn()
  const mockSetActiveView = jest.fn()
  const mockShowToast = jest.fn()
  const mockGetUserStatistics = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()

    mockUseAuth.mockReturnValue({
      user: {
        uid: 'test-user-id',
        email: '<EMAIL>',
        displayName: 'Test User',
        photoURL: null,
        emailVerified: true,
        isAnonymous: false,
        createdAt: Date.now() - (30 * 24 * 60 * 60 * 1000), // 30 days ago
        lastLoginAt: Date.now() - (2 * 60 * 60 * 1000), // 2 hours ago
      },
      deleteAccount: mockDeleteAccount,
      isAuthenticated: true,
      isLoading: false,
      isCreatingUser: false,
      isUserReady: true,
      error: null,
      signInWithGoogle: jest.fn(),
      signInWithEmail: jest.fn(),
      signUpWithEmail: jest.fn(),
      signInAnonymously: jest.fn(),
      signOut: jest.fn(),
      resetPassword: jest.fn(),
      updateProfile: jest.fn(),
    })

    mockUseNavigation.mockReturnValue({
      activeView: 'settings',
      setActiveView: mockSetActiveView,
      timeframeEditingState: null,
      enterTimeframeEditing: jest.fn(),
      exitTimeframeEditing: jest.fn(),
    })

    mockUseToast.mockReturnValue({
      showToast: mockShowToast,
    })

    // Mock the account service methods
    mockGetUserStatistics.mockResolvedValue({
      totalQueues: 2,
      publicQueues: 1,
      privateQueues: 1,
      totalVideos: 8,
      queues: [
        {
          id: 'queue1',
          userId: 'test-user-id',
          isPublic: false,
          metadata: { videoCount: 5, title: 'Test Queue 1' },
          queueData: { items: [] },
          createdAt: Date.now(),
          lastModified: Date.now(),
        },
        {
          id: 'queue2',
          userId: 'test-user-id',
          isPublic: true,
          metadata: { videoCount: 3, title: 'Test Queue 2' },
          queueData: { items: [] },
          createdAt: Date.now(),
          lastModified: Date.now(),
        },
      ]
    })

    mockFirebaseAccountService.getUserStatistics = mockGetUserStatistics
  })

  it('renders account deletion section with user data', async () => {
    render(<AccountDeletionSection />)

    expect(screen.getByText('Danger Zone')).toBeInTheDocument()
    expect(screen.getByText(/This action cannot be undone/)).toBeInTheDocument()
    expect(screen.getByText('Delete My Account')).toBeInTheDocument()

    // Wait for queue data to load
    await waitFor(() => {
      expect(screen.getByText('2')).toBeInTheDocument() // total queues
      expect(screen.getByText('8')).toBeInTheDocument() // total videos (5+3)
    })
  })

  it('opens confirmation dialog when delete button is clicked', async () => {
    render(<AccountDeletionSection />)

    const deleteButton = screen.getByText('Delete My Account')
    fireEvent.click(deleteButton)

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument()
      expect(screen.getByText(/You are about to permanently delete your account/)).toBeInTheDocument()
    })
  })

  it('requires correct confirmation text before allowing deletion', async () => {
    render(<AccountDeletionSection />)

    // Open confirmation dialog
    fireEvent.click(screen.getByText('Delete My Account'))

    await waitFor(() => {
      const confirmButton = screen.getByRole('button', { name: /Delete Account/ })
      expect(confirmButton).toBeDisabled()
    })

    // Type incorrect confirmation
    const input = screen.getByPlaceholderText('DELETE MY ACCOUNT')
    fireEvent.change(input, { target: { value: 'wrong text' } })

    const confirmButton = screen.getByRole('button', { name: /Delete Account/ })
    expect(confirmButton).toBeDisabled()

    // Type correct confirmation
    fireEvent.change(input, { target: { value: 'DELETE MY ACCOUNT' } })
    expect(confirmButton).not.toBeDisabled()
  })

  it('calls deleteAccount when confirmation is completed', async () => {
    mockDeleteAccount.mockResolvedValue(undefined)

    render(<AccountDeletionSection />)

    // Open confirmation dialog
    fireEvent.click(screen.getByText('Delete My Account'))

    await waitFor(() => {
      const input = screen.getByPlaceholderText('DELETE MY ACCOUNT')
      fireEvent.change(input, { target: { value: 'DELETE MY ACCOUNT' } })
    })

    const confirmButton = screen.getByRole('button', { name: /Delete Account/ })
    fireEvent.click(confirmButton)

    await waitFor(() => {
      expect(mockDeleteAccount).toHaveBeenCalledTimes(1)
      expect(mockShowToast).toHaveBeenCalledWith('Account deleted successfully', 'success')
      expect(mockSetActiveView).toHaveBeenCalledWith('search')
    })
  })

  it('handles deletion errors gracefully', async () => {
    const errorMessage = 'Failed to delete account'
    mockDeleteAccount.mockRejectedValue(new Error(errorMessage))

    render(<AccountDeletionSection />)

    // Open confirmation dialog and confirm deletion
    fireEvent.click(screen.getByText('Delete My Account'))

    await waitFor(() => {
      const input = screen.getByPlaceholderText('DELETE MY ACCOUNT')
      fireEvent.change(input, { target: { value: 'DELETE MY ACCOUNT' } })
    })

    const confirmButton = screen.getByRole('button', { name: /Delete Account/ })
    fireEvent.click(confirmButton)

    await waitFor(() => {
      expect(mockShowToast).toHaveBeenCalledWith(
        errorMessage,
        'error'
      )
    })
  })
})
